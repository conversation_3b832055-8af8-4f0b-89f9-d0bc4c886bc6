import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  Wand2, 
  Brain, 
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Clock,
  Target,
  Code,
  Smartphone,
  Monitor,
  Server,
  Package,
  Terminal,
  Wifi,
  WifiOff,
  ArrowRight,
  ArrowLeft,
  FileText,
  Settings,
  Zap,
  Play
} from 'lucide-react'
import { Button } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { AIProjectService, AIProjectRequest } from '../services/aiService'
import { useToast } from './ui/Toast'
import { cn } from '../lib/utils'

// Step definitions for the wizard
const WIZARD_STEPS = [
  {
    id: 'description',
    title: 'Project Description',
    subtitle: 'Tell us about your project idea',
    icon: FileText,
    description: 'Describe what you want to build in your own words'
  },
  {
    id: 'type-tech',
    title: 'Type & Technologies',
    subtitle: 'Choose your project type and tech stack',
    icon: Code,
    description: 'Select the type of project and technologies you want to use'
  },
  {
    id: 'features',
    title: 'Features & Options',
    subtitle: 'Customize your project features',
    icon: Settings,
    description: 'Choose additional features and project complexity'
  },
  {
    id: 'generate',
    title: 'Generate Project',
    subtitle: 'Review and create your project',
    icon: Zap,
    description: 'Review your choices and generate your project structure'
  }
]

const AIProjectGenerator: React.FC = () => {
  const { setCurrentProject, setIsGenerating, incrementFeatureUsed } = useAppStore()
  const { toast } = useToast()
  
  // Wizard state
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set())
  
  const [formData, setFormData] = useState<AIProjectRequest>({
    description: '',
    projectType: 'web',
    technologies: [],
    features: [],
    complexity: 'medium',
    includeTests: true,
    includeDocumentation: true,
  })
  
  const [isGenerating, setLocalIsGenerating] = useState(false)
  const [generatedProject, setGeneratedProject] = useState<any>(null)
  const [aiServiceInfo, setAiServiceInfo] = useState<any>(null)
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking')

  // Check AI service availability on component mount
  useEffect(() => {
    const checkAIService = async () => {
      console.log('🔍 Checking AI service availability...')
      const serviceInfo = AIProjectService.getAIServiceInfo()
      console.log('📊 AI Service Info:', serviceInfo)
      setAiServiceInfo(serviceInfo)

      if (serviceInfo.available) {
        try {
          console.log('🧪 Testing AI connection...')
          const connectionTest = await AIProjectService.testConnection()
          console.log('🔗 Connection test result:', connectionTest)
          setConnectionStatus(connectionTest.success ? 'connected' : 'disconnected')

          if (!connectionTest.success) {
            toast({
              title: 'AI Service Warning',
              description: connectionTest.message,
              variant: 'warning',
            })
          }
        } catch (error) {
          console.error('❌ Connection test error:', error)
          setConnectionStatus('disconnected')
        }
      } else {
        console.log('⚠️ AI service not available')
        setConnectionStatus('disconnected')
      }
    }

    checkAIService()
  }, [])

  // Wizard navigation functions
  const nextStep = () => {
    if (validateCurrentStep()) {
      setCompletedSteps(prev => new Set([...prev, currentStep]))
      setCurrentStep(prev => Math.min(prev + 1, WIZARD_STEPS.length - 1))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const goToStep = (stepIndex: number) => {
    if (stepIndex <= currentStep || completedSteps.has(stepIndex - 1)) {
      setCurrentStep(stepIndex)
    }
  }

  // Validation for each step
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 0: // Description step
        if (!formData.description.trim()) {
          toast({
            title: 'Description Required',
            description: 'Please provide a description of your project',
            variant: 'destructive',
          })
          return false
        }
        if (formData.description.trim().length < 10) {
          toast({
            title: 'Description Too Short',
            description: 'Please provide a more detailed description (at least 10 characters)',
            variant: 'destructive',
          })
          return false
        }
        return true
      
      case 1: // Type & Technologies step
        if (formData.technologies.length === 0) {
          toast({
            title: 'Technologies Required',
            description: 'Please select at least one technology',
            variant: 'destructive',
          })
          return false
        }
        return true
      
      case 2: // Features step
        return true // Features are optional
      
      default:
        return true
    }
  }

  const isStepComplete = (stepIndex: number): boolean => {
    return completedSteps.has(stepIndex) || stepIndex < currentStep
  }

  const canProceedToStep = (stepIndex: number): boolean => {
    return stepIndex <= currentStep || completedSteps.has(stepIndex - 1)
  }

  // Helper functions
  const toggleTechnology = (tech: string) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.includes(tech)
        ? prev.technologies.filter(t => t !== tech)
        : [...prev.technologies, tech]
    }))
  }

  const toggleFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }))
  }

  // Data arrays
  const projectTypes = [
    { id: 'web', name: 'Web Application', description: 'Frontend, fullstack, or SPA', icon: Monitor },
    { id: 'mobile', name: 'Mobile App', description: 'iOS, Android, or cross-platform', icon: Smartphone },
    { id: 'desktop', name: 'Desktop App', description: 'Native desktop application', icon: Monitor },
    { id: 'api', name: 'API/Backend', description: 'REST API or backend service', icon: Server },
    { id: 'library', name: 'Library/Package', description: 'Reusable code library', icon: Package },
    { id: 'cli', name: 'CLI Tool', description: 'Command-line application', icon: Terminal },
  ]

  const availableTechnologies = [
    'React', 'Vue', 'Angular', 'Svelte', 'Next.js', 'Nuxt.js',
    'TypeScript', 'JavaScript', 'Python', 'Node.js', 'Express',
    'FastAPI', 'Django', 'Flask', 'Spring Boot', 'Laravel',
    'Ruby on Rails', 'Go', 'Rust', 'C#', 'Java', 'PHP',
    'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Firebase',
    'Tailwind CSS', 'Bootstrap', 'Material-UI', 'Chakra UI',
    'GraphQL', 'REST API', 'WebSocket', 'Docker', 'Kubernetes'
  ]

  const availableFeatures = [
    'User Authentication', 'Database Integration', 'API Integration',
    'Real-time Features', 'File Upload', 'Email Integration',
    'Payment Processing', 'Search Functionality', 'Admin Dashboard',
    'Multi-language Support', 'Dark Mode', 'Responsive Design',
    'PWA Features', 'SEO Optimization', 'Analytics Integration',
    'Social Media Integration', 'Chat/Messaging', 'Notifications'
  ]

  const handleGenerate = async () => {
    console.log('🚀 Starting project generation...', formData)

    // Final validation before generation
    if (!formData.description.trim() || formData.technologies.length === 0) {
      toast({
        title: 'Incomplete Information',
        description: 'Please complete all required fields before generating',
        variant: 'destructive',
      })
      return
    }

    setLocalIsGenerating(true)
    setIsGenerating(true)

    try {
      console.log('📤 Calling AI service...')
      const result = await AIProjectService.generateProject(formData)
      console.log('✅ Generation successful:', result)

      setGeneratedProject(result)
      setCurrentProject(result.structure)
      incrementFeatureUsed('ai-generation')

      toast({
        title: 'Project Generated Successfully!',
        description: `Your ${result.projectName} project is ready to download.`,
        variant: 'success',
      })
    } catch (error: any) {
      console.error('❌ Generation failed:', error)
      toast({
        title: 'Generation Failed',
        description: error.message || 'Failed to generate project. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLocalIsGenerating(false)
      setIsGenerating(false)
    }
  }

  // Progress Indicator Component
  const ProgressIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        {WIZARD_STEPS.map((step, index) => {
          const Icon = step.icon
          const isActive = index === currentStep
          const isCompleted = isStepComplete(index)
          const canAccess = canProceedToStep(index)

          return (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => canAccess && goToStep(index)}
                disabled={!canAccess}
                className={cn(
                  'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200',
                  isActive && 'border-blue-500 bg-blue-500 text-white shadow-lg scale-110',
                  isCompleted && !isActive && 'border-green-500 bg-green-500 text-white',
                  !isActive && !isCompleted && canAccess && 'border-gray-300 bg-white text-gray-500 hover:border-blue-300',
                  !canAccess && 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
                )}
              >
                {isCompleted && !isActive ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <Icon className="h-5 w-5" />
                )}
              </button>

              {index < WIZARD_STEPS.length - 1 && (
                <div className={cn(
                  'w-12 sm:w-16 md:w-24 h-0.5 mx-2 transition-colors duration-200',
                  isCompleted ? 'bg-green-500' : 'bg-gray-200'
                )} />
              )}
            </div>
          )
        })}
      </div>

      {/* Step info */}
      <div className="text-center">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
          {WIZARD_STEPS[currentStep].title}
        </h2>
        <p className="text-sm sm:text-base text-gray-600 mb-1">
          {WIZARD_STEPS[currentStep].subtitle}
        </p>
        <p className="text-xs sm:text-sm text-gray-500">
          Step {currentStep + 1} of {WIZARD_STEPS.length}
        </p>
      </div>
    </div>
  )

  // Navigation Controls Component
  const NavigationControls = () => (
    <div className="flex items-center justify-between pt-6 border-t border-gray-200">
      <Button
        onClick={prevStep}
        disabled={currentStep === 0}
        variant="outline"
        leftIcon={<ArrowLeft className="h-4 w-4" />}
        className="min-w-[100px]"
      >
        Previous
      </Button>

      <div className="text-xs text-gray-500 hidden sm:block">
        {WIZARD_STEPS[currentStep].description}
      </div>

      {currentStep === WIZARD_STEPS.length - 1 ? (
        <Button
          onClick={handleGenerate}
          disabled={isGenerating}
          loading={isGenerating}
          variant="gradient"
          rightIcon={<Play className="h-4 w-4" />}
          className="min-w-[120px]"
        >
          {isGenerating ? 'Generating...' : 'Generate'}
        </Button>
      ) : (
        <Button
          onClick={nextStep}
          variant="default"
          rightIcon={<ArrowRight className="h-4 w-4" />}
          className="min-w-[100px]"
        >
          Next
        </Button>
      )}
    </div>
  )

  // Step 1: Project Description
  function DescriptionStep() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <FileText className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            What do you want to build?
          </h3>
          <p className="text-gray-600 text-sm sm:text-base">
            Describe your project idea in detail. The more specific you are, the better AI can help you.
          </p>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Project Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Example: I want to build a modern e-commerce website with user authentication, product catalog, shopping cart, and payment integration. It should be responsive and have an admin dashboard for managing products and orders."
            className="w-full h-32 sm:h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-sm sm:text-base"
            required
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>{formData.description.length} characters</span>
            <span>Minimum 10 characters</span>
          </div>
        </div>

        {/* Example suggestions */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">💡 Example project ideas:</h4>
          <div className="grid gap-2 text-xs sm:text-sm">
            {[
              "A task management app with real-time collaboration",
              "A blog platform with markdown support and comments",
              "A portfolio website with project showcase and contact form",
              "A REST API for a social media platform with authentication"
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setFormData(prev => ({ ...prev, description: example }))}
                className="text-left p-2 rounded border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors"
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Step 2: Project Type & Technologies
  function TypeTechStep() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <Code className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Choose your tech stack
          </h3>
          <p className="text-gray-600 text-sm sm:text-base">
            Select the project type and technologies you want to use.
          </p>
        </div>

        {/* Project Type */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Project Type *
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {projectTypes.map(type => (
              <button
                key={type.id}
                onClick={() => setFormData(prev => ({ ...prev, projectType: type.id as any }))}
                className={cn(
                  'p-4 rounded-lg border-2 transition-all duration-200 text-left',
                  formData.projectType === type.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                )}
              >
                <div className="flex items-center gap-3 mb-2">
                  <type.icon className="h-5 w-5" />
                  <span className="font-medium text-sm">{type.name}</span>
                </div>
                <p className="text-xs text-gray-600">{type.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Technologies */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Technologies * (Select at least one)
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {availableTechnologies.map(tech => (
              <button
                key={tech}
                onClick={() => toggleTechnology(tech)}
                className={cn(
                  'px-3 py-2 rounded-lg border text-sm transition-all duration-200',
                  formData.technologies.includes(tech)
                    ? 'border-blue-500 bg-blue-500 text-white'
                    : 'border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                )}
              >
                {tech}
              </button>
            ))}
          </div>
          <p className="text-xs text-gray-500">
            Selected: {formData.technologies.length} technologies
          </p>
        </div>
      </div>
    )
  }

  // Step 3: Features & Options
  function FeaturesStep() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <Settings className="h-12 w-12 text-purple-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Customize your project
          </h3>
          <p className="text-gray-600 text-sm sm:text-base">
            Choose additional features and set the complexity level.
          </p>
        </div>

        {/* Complexity Level */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Project Complexity
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {[
              { id: 'simple', name: 'Simple', description: 'Basic structure, minimal features', time: '1-2 weeks' },
              { id: 'medium', name: 'Medium', description: 'Balanced features and structure', time: '3-6 weeks' },
              { id: 'complex', name: 'Complex', description: 'Advanced features and architecture', time: '2-4 months' }
            ].map(level => (
              <button
                key={level.id}
                onClick={() => setFormData(prev => ({ ...prev, complexity: level.id as any }))}
                className={cn(
                  'p-4 rounded-lg border-2 transition-all duration-200 text-left',
                  formData.complexity === level.id
                    ? 'border-purple-500 bg-purple-50 text-purple-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                )}
              >
                <div className="font-medium text-sm mb-1">{level.name}</div>
                <div className="text-xs text-gray-600 mb-2">{level.description}</div>
                <div className="text-xs text-gray-500">Est. time: {level.time}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Additional Features */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Additional Features (Optional)
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {availableFeatures.map(feature => (
              <button
                key={feature}
                onClick={() => toggleFeature(feature)}
                className={cn(
                  'p-3 rounded-lg border text-sm transition-all duration-200 text-left',
                  formData.features.includes(feature)
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-300 hover:border-green-300 hover:bg-green-50'
                )}
              >
                <div className="flex items-center gap-2">
                  {formData.features.includes(feature) && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  <span>{feature}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Project Options */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Project Options
          </label>
          <div className="space-y-3">
            <label className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.includeTests}
                onChange={(e) => setFormData(prev => ({ ...prev, includeTests: e.target.checked }))}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <div>
                <div className="text-sm font-medium">Include Testing Setup</div>
                <div className="text-xs text-gray-600">Add test files and configuration</div>
              </div>
            </label>

            <label className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.includeDocumentation}
                onChange={(e) => setFormData(prev => ({ ...prev, includeDocumentation: e.target.checked }))}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <div>
                <div className="text-sm font-medium">Include Documentation</div>
                <div className="text-xs text-gray-600">Add README and documentation files</div>
              </div>
            </label>
          </div>
        </div>
      </div>
    )
  }

  // Step 4: Generate Project
  function GenerateStep() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <Zap className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Ready to generate!
          </h3>
          <p className="text-gray-600 text-sm sm:text-base">
            Review your choices and generate your project structure.
          </p>
        </div>

        {/* Project Summary */}
        <div className="bg-gray-50 rounded-lg p-4 sm:p-6 space-y-4">
          <h4 className="font-medium text-gray-900 mb-4">Project Summary</h4>

          <div className="grid gap-4">
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Description</label>
              <p className="text-sm text-gray-900 mt-1">{formData.description}</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Project Type</label>
                <p className="text-sm text-gray-900 mt-1 capitalize">{formData.projectType}</p>
              </div>

              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Complexity</label>
                <p className="text-sm text-gray-900 mt-1 capitalize">{formData.complexity}</p>
              </div>
            </div>

            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Technologies</label>
              <div className="flex flex-wrap gap-1 mt-1">
                {formData.technologies.map(tech => (
                  <span key={tech} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            {formData.features.length > 0 && (
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Features</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {formData.features.map(feature => (
                    <span key={feature} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Include Tests</label>
                <p className="text-sm text-gray-900 mt-1">{formData.includeTests ? 'Yes' : 'No'}</p>
              </div>

              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Include Documentation</label>
                <p className="text-sm text-gray-900 mt-1">{formData.includeDocumentation ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* AI Service Status for Generation */}
        {connectionStatus === 'connected' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-green-800 text-sm">
              <CheckCircle className="h-4 w-4" />
              <span>AI service is ready. Your project will be generated using Google Gemini AI.</span>
            </div>
          </div>
        )}

        {connectionStatus === 'disconnected' && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-orange-800 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>AI service unavailable. Your project will be generated using our fallback system.</span>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Step Content Renderer
  function renderStepContent() {
    switch (currentStep) {
      case 0:
        return <DescriptionStep />
      case 1:
        return <TypeTechStep />
      case 2:
        return <FeaturesStep />
      case 3:
        return <GenerateStep />
      default:
        return <DescriptionStep />
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header with AI Service Status */}
      <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-4 sm:p-6 border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-lg sm:text-xl font-semibold text-gray-900">AI Project Generator</h1>
              <p className="text-sm sm:text-base text-gray-600">Create your perfect project structure with AI</p>
            </div>
          </div>

          {/* AI Service Status */}
          <div className="flex items-center gap-2 text-xs sm:text-sm">
            {connectionStatus === 'checking' && (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="animate-spin h-3 w-3 sm:h-4 sm:w-4 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
                <span>Checking AI service...</span>
              </div>
            )}

            {connectionStatus === 'connected' && aiServiceInfo && (
              <div className="flex items-center gap-2 text-green-600">
                <Wifi className="h-3 w-3 sm:h-4 sm:w-4" />
                <span>Powered by {aiServiceInfo.provider}</span>
              </div>
            )}

            {connectionStatus === 'disconnected' && (
              <div className="flex items-center gap-2 text-orange-600">
                <WifiOff className="h-3 w-3 sm:h-4 sm:w-4" />
                <span>AI service unavailable</span>
              </div>
            )}
          </div>
        </div>

        {/* Service unavailable warning */}
        {connectionStatus === 'disconnected' && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mt-4">
            <div className="flex items-start gap-2 text-orange-800 text-xs sm:text-sm">
              <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>
                AI service is currently unavailable. The generator will use a basic fallback system.
                {!aiServiceInfo?.available && ' Please configure the VITE_GEMINI_API_KEY environment variable.'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Progress Indicator */}
      <ProgressIndicator />

      {/* Main Wizard Content */}
      <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Controls */}
        <NavigationControls />
      </div>

      {/* Generated Project Info */}
      {generatedProject && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border p-4 sm:p-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900">Project Generated Successfully!</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Estimated Time: {generatedProject.estimatedTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Difficulty: {generatedProject.difficulty}</span>
            </div>
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">AI Generated</span>
            </div>
          </div>

          {generatedProject.recommendations.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                AI Recommendations:
              </h4>
              <ul className="space-y-1">
                {generatedProject.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                    <span className="text-yellow-500 mt-1">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      )}
    </div>
  )
}

export default AIProjectGenerator
