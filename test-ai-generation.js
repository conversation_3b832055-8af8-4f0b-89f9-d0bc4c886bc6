#!/usr/bin/env node

/**
 * Test script to verify AI project generation in browser environment
 * This simulates what happens when the user clicks generate in the browser
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '.env.local');

if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.log('⚠️  .env.local file not found');
}

const API_KEY = process.env.VITE_GEMINI_API_KEY;

console.log('🧪 Testing AI Project Generation (Browser Simulation)...\n');

if (!API_KEY) {
  console.error('❌ Error: VITE_GEMINI_API_KEY not found');
  process.exit(1);
}

console.log('✅ API Key found:', API_KEY.substring(0, 10) + '...');

// Simulate the exact request that would be sent from the browser
const testRequest = {
  description: 'A simple todo app with React',
  projectType: 'web',
  technologies: ['React', 'TypeScript'],
  features: ['CRUD Operations', 'Local Storage'],
  complexity: 'medium',
  includeTests: true,
  includeDocumentation: true
};

async function testProjectGeneration() {
  try {
    console.log('🚀 Simulating browser AI generation request...');
    console.log('📋 Request data:', JSON.stringify(testRequest, null, 2));

    const genAI = new GoogleGenerativeAI(API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Build the same prompt that the browser would send
    const prompt = `You are an expert software architect. Generate a complete project structure for the following requirements:

Project Description: ${testRequest.description}
Project Type: ${testRequest.projectType}
Technologies: ${testRequest.technologies.join(', ')}
Features: ${testRequest.features.join(', ')}
Complexity: ${testRequest.complexity}
Include Tests: ${testRequest.includeTests}
Include Documentation: ${testRequest.includeDocumentation}

Please respond with a JSON object in the following format:
{
  "projectName": "project-name",
  "structure": {
    "name": "project-root",
    "type": "directory",
    "children": [
      {
        "name": "src",
        "type": "directory", 
        "children": [
          {
            "name": "App.tsx",
            "type": "file",
            "content": "// React component code here"
          }
        ]
      }
    ]
  },
  "recommendations": ["recommendation 1", "recommendation 2"],
  "estimatedTime": "1-2 weeks",
  "difficulty": "Beginner"
}

Make sure the response is valid JSON and includes realistic file contents for a ${testRequest.projectType} project using ${testRequest.technologies.join(' and ')}.`;

    console.log('\n📤 Sending prompt to Gemini...');
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('📥 Response received, length:', text.length);
    console.log('📄 First 200 chars:', text.substring(0, 200) + '...');

    // Try to parse the JSON response
    try {
      const jsonMatch = text.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                       text.match(/(\{[\s\S]*\})/);

      if (!jsonMatch) {
        console.log('❌ No JSON found in response');
        console.log('Full response:', text);
        return false;
      }

      const parsed = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      
      console.log('✅ JSON parsing successful!');
      console.log('📋 Project name:', parsed.projectName);
      console.log('🏗️  Structure type:', parsed.structure?.type);
      console.log('📁 Root children count:', parsed.structure?.children?.length || 0);
      console.log('💡 Recommendations count:', parsed.recommendations?.length || 0);
      console.log('⏱️  Estimated time:', parsed.estimatedTime);
      console.log('📊 Difficulty:', parsed.difficulty);

      // Validate structure
      if (parsed.structure && parsed.structure.children) {
        console.log('\n🔍 Structure validation:');
        console.log('   Root name:', parsed.structure.name);
        console.log('   Root type:', parsed.structure.type);
        console.log('   Children:', parsed.structure.children.map(c => `${c.name} (${c.type})`).join(', '));
      }

      return true;
    } catch (parseError) {
      console.log('❌ JSON parsing failed:', parseError.message);
      console.log('Raw response:', text.substring(0, 500) + '...');
      return false;
    }

  } catch (error) {
    console.error('❌ Generation test failed:', error.message);
    return false;
  }
}

// Run the test
testProjectGeneration().then(success => {
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULT:', success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('='.repeat(60));
  
  if (success) {
    console.log('🎉 AI project generation is working correctly!');
    console.log('✅ The browser should be able to generate projects.');
  } else {
    console.log('⚠️  AI project generation has issues.');
    console.log('🔧 Check the errors above for troubleshooting.');
  }
}).catch(console.error);
