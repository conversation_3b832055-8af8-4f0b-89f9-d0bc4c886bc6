export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  data?: any
  source?: string
  userId?: string
  sessionId?: string
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableStorage: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  maxStorageEntries: number
}

class Logger {
  private config: LoggerConfig
  private sessionId: string
  private userId?: string

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      maxStorageEntries: 1000,
      ...config,
    }
    
    this.sessionId = this.generateSessionId()
    this.initializeStorage()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeStorage(): void {
    if (!this.config.enableStorage) return
    
    try {
      const existingLogs = localStorage.getItem('projectforge_logs')
      if (!existingLogs) {
        localStorage.setItem('projectforge_logs', JSON.stringify([]))
      }
    } catch (error) {
      console.warn('Failed to initialize log storage:', error)
    }
  }

  setUserId(userId: string): void {
    this.userId = userId
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level
  }

  private createLogEntry(level: LogLevel, message: string, data?: any, source?: string): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      source,
      userId: this.userId,
      sessionId: this.sessionId,
    }
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return

    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR']
    const levelColors = ['#6B7280', '#3B82F6', '#F59E0B', '#EF4444']
    
    const style = `color: ${levelColors[entry.level]}; font-weight: bold;`
    const prefix = `[${levelNames[entry.level]}] ${entry.timestamp}`
    
    if (entry.data) {
      console.groupCollapsed(`%c${prefix} ${entry.message}`, style)
      console.log('Data:', entry.data)
      if (entry.source) console.log('Source:', entry.source)
      console.log('Session:', entry.sessionId)
      if (entry.userId) console.log('User:', entry.userId)
      console.groupEnd()
    } else {
      console.log(`%c${prefix} ${entry.message}`, style)
    }
  }

  private logToStorage(entry: LogEntry): void {
    if (!this.config.enableStorage) return

    try {
      const logs = JSON.parse(localStorage.getItem('projectforge_logs') || '[]')
      logs.push(entry)
      
      // Keep only the most recent entries
      if (logs.length > this.config.maxStorageEntries) {
        logs.splice(0, logs.length - this.config.maxStorageEntries)
      }
      
      localStorage.setItem('projectforge_logs', JSON.stringify(logs))
    } catch (error) {
      console.warn('Failed to store log entry:', error)
    }
  }

  private async logToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.enableRemote || !this.config.remoteEndpoint) return

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      })
    } catch (error) {
      console.warn('Failed to send log to remote endpoint:', error)
    }
  }

  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    if (!this.shouldLog(level)) return

    const entry = this.createLogEntry(level, message, data, source)
    
    this.logToConsole(entry)
    this.logToStorage(entry)
    
    if (this.config.enableRemote) {
      this.logToRemote(entry).catch(() => {
        // Silently fail for remote logging
      })
    }
  }

  debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source)
  }

  info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source)
  }

  warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source)
  }

  error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source)
  }

  // Convenience methods for common scenarios
  userAction(action: string, data?: any): void {
    this.info(`User action: ${action}`, data, 'user-interaction')
  }

  apiCall(endpoint: string, method: string, data?: any): void {
    this.debug(`API call: ${method} ${endpoint}`, data, 'api')
  }

  performance(operation: string, duration: number, data?: any): void {
    this.info(`Performance: ${operation} took ${duration}ms`, data, 'performance')
  }

  // Get stored logs
  getLogs(level?: LogLevel): LogEntry[] {
    try {
      const logs = JSON.parse(localStorage.getItem('projectforge_logs') || '[]')
      if (level !== undefined) {
        return logs.filter((log: LogEntry) => log.level >= level)
      }
      return logs
    } catch (error) {
      console.warn('Failed to retrieve logs:', error)
      return []
    }
  }

  // Clear stored logs
  clearLogs(): void {
    try {
      localStorage.removeItem('projectforge_logs')
      this.initializeStorage()
      this.info('Logs cleared', undefined, 'logger')
    } catch (error) {
      console.warn('Failed to clear logs:', error)
    }
  }

  // Export logs as JSON
  exportLogs(): string {
    const logs = this.getLogs()
    return JSON.stringify(logs, null, 2)
  }

  // Get session info
  getSessionInfo(): { sessionId: string; userId?: string } {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
    }
  }
}

// Create default logger instance
export const logger = new Logger({
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableStorage: true,
  enableRemote: false, // Enable this when you have a logging service
})

// Performance monitoring utilities
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map()

  static start(operation: string): void {
    this.timers.set(operation, performance.now())
    logger.debug(`Performance timer started: ${operation}`, undefined, 'performance')
  }

  static end(operation: string, data?: any): number {
    const startTime = this.timers.get(operation)
    if (!startTime) {
      logger.warn(`Performance timer not found: ${operation}`, undefined, 'performance')
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(operation)
    
    logger.performance(operation, duration, data)
    return duration
  }

  static measure<T>(operation: string, fn: () => T, data?: any): T {
    this.start(operation)
    try {
      const result = fn()
      this.end(operation, data)
      return result
    } catch (error) {
      this.end(operation, { ...data, error: error.message })
      throw error
    }
  }

  static async measureAsync<T>(operation: string, fn: () => Promise<T>, data?: any): Promise<T> {
    this.start(operation)
    try {
      const result = await fn()
      this.end(operation, data)
      return result
    } catch (error) {
      this.end(operation, { ...data, error: error.message })
      throw error
    }
  }
}

// Error tracking utilities
export const trackError = (error: Error, context?: any): void => {
  logger.error(error.message, {
    stack: error.stack,
    context,
    url: window.location.href,
    userAgent: navigator.userAgent,
  }, 'error-tracking')
}

export const trackUserAction = (action: string, data?: any): void => {
  logger.userAction(action, data)
}

export const trackPageView = (page: string): void => {
  logger.info(`Page view: ${page}`, {
    url: window.location.href,
    referrer: document.referrer,
    timestamp: Date.now(),
  }, 'analytics')
}
