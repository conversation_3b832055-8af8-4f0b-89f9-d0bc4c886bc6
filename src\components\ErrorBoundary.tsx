import React, { Component, ErrorInfo, ReactNode } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>riangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Copy,
  ExternalLink 
} from 'lucide-react'
import { <PERSON><PERSON> } from './ui/Button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  }

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo,
    })

    // Log error to analytics service
    this.logErrorToService(error, errorInfo)
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real app, you'd send this to your error tracking service
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    console.log('Error logged:', errorData)
    
    // Example: Send to Sentry, LogRocket, or your own service
    // Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private copyErrorDetails = async () => {
    const { error, errorInfo } = this.state
    const errorText = `
Error: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
    `.trim()

    try {
      await navigator.clipboard.writeText(errorText)
      alert('Error details copied to clipboard')
    } catch (err) {
      console.error('Failed to copy error details:', err)
    }
  }

  private reportIssue = () => {
    const { error } = this.state
    const title = encodeURIComponent(`Bug Report: ${error?.message || 'Unknown Error'}`)
    const body = encodeURIComponent(`
**Error Description:**
${error?.message || 'Unknown error occurred'}

**Steps to Reproduce:**
1. 
2. 
3. 

**Expected Behavior:**


**Actual Behavior:**


**Additional Context:**
- URL: ${window.location.href}
- Timestamp: ${new Date().toISOString()}
- User Agent: ${navigator.userAgent}

**Error Stack:**
\`\`\`
${error?.stack || 'No stack trace available'}
\`\`\`
    `)

    const githubUrl = `https://github.com/your-repo/issues/new?title=${title}&body=${body}`
    window.open(githubUrl, '_blank')
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden"
          >
            {/* Header */}
            <div className="bg-red-500 p-6 text-white">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <AlertTriangle className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold">Oops! Something went wrong</h1>
                  <p className="text-red-100 text-sm">
                    We encountered an unexpected error
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              <div className="text-gray-600">
                <p className="mb-2">
                  Don't worry! This error has been logged and we'll look into it.
                </p>
                <p className="text-sm">
                  You can try refreshing the page or going back to the homepage.
                </p>
              </div>

              {/* Error Details (Collapsible) */}
              <details className="bg-gray-50 rounded-lg p-4">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                  Technical Details
                </summary>
                <div className="mt-3 space-y-2">
                  <div className="text-xs">
                    <strong>Error:</strong>
                    <pre className="mt-1 bg-white p-2 rounded border text-red-600 overflow-auto">
                      {this.state.error?.message}
                    </pre>
                  </div>
                  {this.state.error?.stack && (
                    <div className="text-xs">
                      <strong>Stack Trace:</strong>
                      <pre className="mt-1 bg-white p-2 rounded border text-gray-600 overflow-auto max-h-32">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>

              {/* Action Buttons */}
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={this.handleRetry}
                    variant="default"
                    leftIcon={<RefreshCw className="h-4 w-4" />}
                  >
                    Try Again
                  </Button>
                  <Button
                    onClick={this.handleGoHome}
                    variant="outline"
                    leftIcon={<Home className="h-4 w-4" />}
                  >
                    Go Home
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={this.copyErrorDetails}
                    variant="ghost"
                    size="sm"
                    leftIcon={<Copy className="h-4 w-4" />}
                  >
                    Copy Details
                  </Button>
                  <Button
                    onClick={this.reportIssue}
                    variant="ghost"
                    size="sm"
                    leftIcon={<Bug className="h-4 w-4" />}
                  >
                    Report Issue
                  </Button>
                </div>

                <Button
                  onClick={this.handleReload}
                  variant="outline"
                  className="w-full"
                  leftIcon={<RefreshCw className="h-4 w-4" />}
                >
                  Reload Page
                </Button>
              </div>

              {/* Help Text */}
              <div className="text-xs text-gray-500 text-center">
                <p>
                  If this problem persists, please{' '}
                  <button
                    onClick={this.reportIssue}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    report it
                  </button>{' '}
                  so we can fix it.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook for functional components to handle errors
// eslint-disable-next-line react-refresh/only-export-components
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: any) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo)
    
    // You could also trigger a toast notification here
    // toast({ title: 'Error', description: error.message, variant: 'destructive' })
  }

  return { handleError }
}

// Higher-order component for wrapping components with error boundary
// eslint-disable-next-line react-refresh/only-export-components
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

export default ErrorBoundary
