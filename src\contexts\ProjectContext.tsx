import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Project, FileNode } from '../types';

interface ProjectContextType {
  project: Project;
  setProject: (project: Project) => void;
  isGenerating: boolean;
  setIsGenerating: (isGenerating: boolean) => void;
}

const defaultProject: Project = {
  name: '',
  frontend: null,
  backend: null,
  database: null,
  structure: null
};

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [project, setProject] = useState<Project>(defaultProject);
  const [isGenerating, setIsGenerating] = useState(false);

  return (
    <ProjectContext.Provider value={{ project, setProject, isGenerating, setIsGenerating }}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProjectContext = () => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
};