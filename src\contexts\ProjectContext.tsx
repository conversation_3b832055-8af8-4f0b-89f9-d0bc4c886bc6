import React, { createContext, useState, ReactNode } from 'react';
import { Project } from '../types';

interface ProjectContextType {
  project: Project;
  setProject: (project: Project) => void;
  isGenerating: boolean;
  setIsGenerating: (isGenerating: boolean) => void;
}

const defaultProject: Project = {
  name: '',
  frontend: null,
  backend: null,
  database: null,
  structure: null
};

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [project, setProject] = useState<Project>(defaultProject);
  const [isGenerating, setIsGenerating] = useState(false);

  return (
    <ProjectContext.Provider value={{ project, setProject, isGenerating, setIsGenerating }}>
      {children}
    </ProjectContext.Provider>
  );
};

// Export hook separately to avoid Fast Refresh issues
export { useProjectContext } from '../hooks/useProjectContext';