import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, Copy, Github, Share2, Heart, Star, ExternalLink } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';
import { generateZip } from '../utils/zipGenerator';
import { Button } from './ui/Button';
import { useToast } from './ui/Toast';
import { copyToClipboard } from '../lib/utils';

const DownloadSection = () => {
  const {
    currentProject,
    addToHistory,
    incrementProjectsGenerated,
    incrementFeatureUsed
  } = useAppStore();
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadCount, setDownloadCount] = useState(0);

  if (!currentProject) return null;

  const handleDownload = async () => {
    if (!currentProject.structure) return;

    setIsDownloading(true);
    incrementFeatureUsed('download');

    try {
      await generateZip(currentProject);
      setDownloadCount(prev => prev + 1);

      // Add to history if it's the first download
      if (downloadCount === 0) {
        addToHistory(currentProject);
        incrementProjectsGenerated();
      }

      toast({
        title: 'Download Started',
        description: `${currentProject.name}.zip is being downloaded`,
        variant: 'success',
      });
    } catch (error) {
      console.error('Error generating zip:', error);
      toast({
        title: 'Download Failed',
        description: 'Failed to generate ZIP file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleCopyStructure = async () => {
    if (!currentProject.structure) return;

    try {
      const structureText = generateStructureText(currentProject.structure);
      await copyToClipboard(structureText);
      incrementFeatureUsed('copy-structure');

      toast({
        title: 'Structure Copied',
        description: 'Project structure copied to clipboard',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy structure to clipboard',
        variant: 'destructive',
      });
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${currentProject.name} - Project Structure`,
          text: `Check out this project structure generated with ProjectForge`,
          url: window.location.href,
        });
        incrementFeatureUsed('share');
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      try {
        await copyToClipboard(window.location.href);
        toast({
          title: 'Link Copied',
          description: 'Project link copied to clipboard',
          variant: 'success',
        });
      } catch (error) {
        toast({
          title: 'Share Failed',
          description: 'Failed to share project',
          variant: 'destructive',
        });
      }
    }
  };

  const generateStructureText = (node: any, depth = 0): string => {
    const indent = '  '.repeat(depth);
    let result = `${indent}${node.name}${node.type === 'directory' ? '/' : ''}\n`;

    if (node.children) {
      for (const child of node.children) {
        result += generateStructureText(child, depth + 1);
      }
    }

    return result;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      {/* Project Info Card */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Github className="h-5 w-5 text-green-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{currentProject.name}</h3>
            <p className="text-sm text-gray-600">
              {currentProject.frontend && `${currentProject.frontend}`}
              {currentProject.backend && ` • ${currentProject.backend}`}
              {currentProject.database && ` • ${currentProject.database}`}
            </p>
          </div>
          {downloadCount > 0 && (
            <div className="text-right">
              <div className="text-sm font-medium text-green-600">{downloadCount}</div>
              <div className="text-xs text-gray-500">downloads</div>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          onClick={handleDownload}
          loading={isDownloading}
          disabled={isDownloading}
          variant="gradient"
          size="lg"
          className="w-full"
          leftIcon={<Download className="h-5 w-5" />}
        >
          {isDownloading ? 'Generating ZIP...' : 'Download Project'}
        </Button>

        <div className="grid grid-cols-2 gap-3">
          <Button
            onClick={handleCopyStructure}
            variant="outline"
            leftIcon={<Copy className="h-4 w-4" />}
          >
            Copy Structure
          </Button>

          <Button
            onClick={handleShare}
            variant="outline"
            leftIcon={<Share2 className="h-4 w-4" />}
          >
            Share
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => {
              incrementFeatureUsed('github-template');
              window.open('https://github.com/new', '_blank');
            }}
            className="flex items-center gap-2 p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
          >
            <Github className="h-4 w-4" />
            Create on GitHub
          </button>

          <button
            onClick={() => {
              incrementFeatureUsed('deploy-vercel');
              window.open('https://vercel.com/new', '_blank');
            }}
            className="flex items-center gap-2 p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
          >
            <ExternalLink className="h-4 w-4" />
            Deploy to Vercel
          </button>
        </div>
      </div>

      {/* Success Message */}
      {downloadCount > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-green-50 border border-green-200 rounded-lg p-4"
        >
          <div className="flex items-center gap-2 text-green-800">
            <Star className="h-4 w-4" />
            <span className="text-sm font-medium">
              Project downloaded successfully! Ready to start coding? 🚀
            </span>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default DownloadSection;