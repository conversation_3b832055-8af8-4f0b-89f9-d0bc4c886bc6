import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronRight, File, Folder, FileEdit, Copy, Eye } from 'lucide-react';
import { FileNode } from '../types';
import { getFileIcon, copyToClipboard } from '../lib/utils';
import { useToast } from './ui/Toast';

interface FileTreeItemProps {
  node: FileNode;
  level: number;
  onSelectFile: (node: FileNode) => void;
}

interface FileTreePreviewProps {
  structure: FileNode;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({ node, level, onSelectFile }) => {
  const [isOpen, setIsOpen] = useState(level < 2);
  const hasChildren = node.children && node.children.length > 0;
  const { toast } = useToast();

  const toggleOpen = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelectFile = () => {
    if (!hasChildren) {
      onSelectFile(node);
    }
  };

  const handleCopyPath = async (e: React.MouseEvent, path: string) => {
    e.stopPropagation();
    try {
      await copyToClipboard(path);
      toast({
        title: 'Path Copied',
        description: `Copied "${path}" to clipboard`,
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy path to clipboard',
        variant: 'destructive',
      });
    }
  };

  const getNodePath = (nodeName: string, currentLevel: number): string => {
    // This is a simplified path - in a real implementation, you'd track the full path
    return nodeName;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="select-none"
    >
      <div
        className={`group flex items-center py-1.5 px-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors ${
          !hasChildren ? 'hover:text-blue-700' : ''
        }`}
        onClick={hasChildren ? toggleOpen : handleSelectFile}
        style={{ paddingLeft: `${level * 16}px` }}
      >
        <div className="flex items-center flex-1 min-w-0">
          {hasChildren ? (
            isOpen ? <ChevronDown size={14} className="mr-1 text-gray-400 flex-shrink-0" />
                  : <ChevronRight size={14} className="mr-1 text-gray-400 flex-shrink-0" />
          ) : (
            <span className="w-4 mr-1 flex-shrink-0"></span>
          )}

          {hasChildren ? (
            <Folder size={16} className="mr-2 text-blue-500 flex-shrink-0" />
          ) : (
            <span className="mr-2 text-lg flex-shrink-0">{getFileIcon(node.name)}</span>
          )}

          <span className="text-sm text-gray-700 truncate">{node.name}</span>

          {hasChildren && node.children && (
            <span className="ml-2 text-xs text-gray-400 flex-shrink-0">
              ({node.children.length})
            </span>
          )}
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {!hasChildren && node.content && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSelectFile(node);
              }}
              className="p-1 rounded hover:bg-gray-200 transition-colors"
              title="Preview content"
            >
              <Eye size={12} className="text-gray-500" />
            </button>
          )}
          <button
            onClick={(e) => handleCopyPath(e, getNodePath(node.name, level))}
            className="p-1 rounded hover:bg-gray-200 transition-colors"
            title="Copy path"
          >
            <Copy size={12} className="text-gray-500" />
          </button>
        </div>
      </div>

      <AnimatePresence>
        {isOpen && hasChildren && node.children && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {node.children.map((child, index) => (
              <FileTreeItem
                key={`${child.name}-${index}`}
                node={child}
                level={level + 1}
                onSelectFile={onSelectFile}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const FileTreePreview: React.FC<FileTreePreviewProps> = ({ structure }) => {
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  
  const handleSelectFile = (node: FileNode) => {
    setSelectedFile(node);
  };
  
  const handleClose = () => {
    setSelectedFile(null);
  };

  return (
    <div className="text-gray-800">
      {selectedFile ? (
        <div className="h-full">
          <div className="flex justify-between items-center mb-3 pb-2 border-b">
            <div className="flex items-center">
              <File size={16} className="mr-2 text-blue-500" />
              <h3 className="font-medium">{selectedFile.name}</h3>
            </div>
            <button 
              onClick={handleClose}
              className="text-sm px-2 py-1 text-gray-600 hover:text-gray-800"
            >
              Back to tree
            </button>
          </div>
          
          <div className="h-[500px] overflow-auto">
            <pre className="text-xs bg-gray-50 p-4 rounded border font-mono whitespace-pre-wrap">
              {selectedFile.content || '// No content generated for this file yet'}
            </pre>
          </div>
        </div>
      ) : (
        <FileTreeItem 
          node={structure} 
          level={0} 
          onSelectFile={handleSelectFile} 
        />
      )}
    </div>
  );
};

export default FileTreePreview;