import React from 'react'
import { motion } from 'framer-motion'
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'
import {
  TrendingUp,
  Download,
  Heart,
  Clock,
  Users,
  Code,
  Zap,
  Target
} from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

const COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#6B7280']

const AnalyticsDashboard: React.FC = () => {
  const { analytics, projectHistory, favoriteTemplates, customTemplates } = useAppStore()

  // Prepare data for charts
  const templateUsageData = Object.entries(analytics.templatesUsed).map(([template, count]) => ({
    name: template,
    value: count,
  }))

  const featureUsageData = Object.entries(analytics.featuresUsed).map(([feature, count]) => ({
    name: feature.replace('-', ' '),
    count,
  }))

  // Mock time series data for project generation over time
  const timeSeriesData = [
    { month: 'Jan', projects: 12 },
    { month: 'Feb', projects: 19 },
    { month: 'Mar', projects: 25 },
    { month: 'Apr', projects: 31 },
    { month: 'May', projects: 28 },
    { month: 'Jun', projects: 35 },
  ]

  const stats = [
    {
      title: 'Total Projects',
      value: analytics.projectsGenerated,
      icon: Code,
      color: 'blue',
      change: '+12%',
    },
    {
      title: 'Templates Used',
      value: Object.keys(analytics.templatesUsed).length,
      icon: Download,
      color: 'green',
      change: '+8%',
    },
    {
      title: 'Favorites',
      value: favoriteTemplates.length,
      icon: Heart,
      color: 'red',
      change: '+15%',
    },
    {
      title: 'Custom Templates',
      value: customTemplates.length,
      icon: Zap,
      color: 'purple',
      change: '+25%',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center gap-3 mb-2">
          <TrendingUp className="h-8 w-8" />
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        </div>
        <p className="text-blue-100">Track your project generation patterns and usage statistics</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm text-${stat.color}-600`}>{stat.change} from last month</p>
                </div>
                <div className={`p-3 bg-${stat.color}-100 rounded-lg`}>
                  <Icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Template Usage Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Template Usage</h3>
          {templateUsageData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={templateUsageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {templateUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-[300px] flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Target className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No template usage data yet</p>
              </div>
            </div>
          )}
        </div>

        {/* Feature Usage Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Feature Usage</h3>
          {featureUsageData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={featureUsageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-[300px] flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Zap className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No feature usage data yet</p>
              </div>
            </div>
          )}
        </div>

        {/* Project Generation Trend */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Generation Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="projects" 
                stroke="#8B5CF6" 
                fill="#8B5CF6" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {projectHistory.slice(0, 5).map((project, index) => (
              <div key={project.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Code className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{project.project.name}</p>
                  <p className="text-sm text-gray-600">
                    Created {new Date(project.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {project.downloadCount} downloads
                </div>
              </div>
            ))}
            {projectHistory.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No recent activity</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Insights & Recommendations</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Most Popular Template</h4>
            <p className="text-sm text-blue-700">
              {templateUsageData.length > 0 
                ? templateUsageData.reduce((a, b) => a.value > b.value ? a : b).name
                : 'No data yet'
              }
            </p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Most Used Feature</h4>
            <p className="text-sm text-green-700">
              {featureUsageData.length > 0
                ? featureUsageData.reduce((a, b) => a.count > b.count ? a : b).name
                : 'No data yet'
              }
            </p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Productivity Score</h4>
            <p className="text-sm text-purple-700">
              {analytics.projectsGenerated > 10 ? 'High' : 
               analytics.projectsGenerated > 5 ? 'Medium' : 'Getting Started'}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsDashboard
