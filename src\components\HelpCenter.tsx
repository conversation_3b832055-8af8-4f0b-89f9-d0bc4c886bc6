import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  HelpCircle, 
  Search, 
  Book, 
  Video, 
  MessageCircle, 
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Lightbulb,
  Zap,
  Code,
  Download,
  Star
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { cn } from '../lib/utils'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
}

interface GuideItem {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  estimatedTime: string
  steps: string[]
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do I generate a project structure?',
    answer: 'You can generate a project structure in three ways: 1) Browse our template library and select a pre-built template, 2) Use our AI generator by describing your project, or 3) Create a custom structure by pasting your own folder structure.',
    category: 'Getting Started',
    tags: ['templates', 'ai', 'custom'],
  },
  {
    id: '2',
    question: 'What file formats are supported for download?',
    answer: 'Currently, we support ZIP file downloads. The ZIP contains all your project files with the proper folder structure and basic file contents.',
    category: 'Downloads',
    tags: ['zip', 'download', 'files'],
  },
  {
    id: '3',
    question: 'Can I save my generated projects?',
    answer: 'Yes! All your generated projects are automatically saved to your project history. You can access them from the History tab and re-download or modify them anytime.',
    category: 'Project Management',
    tags: ['history', 'save', 'projects'],
  },
  {
    id: '4',
    question: 'How does the AI project generator work?',
    answer: 'Our AI analyzes your project description, selected technologies, and requirements to generate a tailored project structure. It considers best practices, common patterns, and modern development standards.',
    category: 'AI Features',
    tags: ['ai', 'generator', 'description'],
  },
  {
    id: '5',
    question: 'Can I customize the generated templates?',
    answer: 'Absolutely! After generating a project, you can view the structure and make modifications. You can also create custom templates and save them for future use.',
    category: 'Customization',
    tags: ['customize', 'templates', 'modify'],
  },
]

const guides: GuideItem[] = [
  {
    id: 'quick-start',
    title: 'Quick Start Guide',
    description: 'Get up and running with ProjectForge in under 5 minutes',
    icon: Zap,
    difficulty: 'Beginner',
    estimatedTime: '5 minutes',
    steps: [
      'Choose a template from our library or describe your project to the AI',
      'Preview the generated structure in the sidebar',
      'Download your project as a ZIP file',
      'Extract and start coding!',
    ],
  },
  {
    id: 'ai-generator',
    title: 'Using the AI Generator',
    description: 'Learn how to get the best results from our AI-powered project generator',
    icon: Lightbulb,
    difficulty: 'Intermediate',
    estimatedTime: '10 minutes',
    steps: [
      'Write a clear, detailed project description',
      'Select relevant technologies and frameworks',
      'Choose appropriate features for your project',
      'Set the complexity level based on your needs',
      'Review AI recommendations and generate',
    ],
  },
  {
    id: 'custom-templates',
    title: 'Creating Custom Templates',
    description: 'Build and save your own project templates for reuse',
    icon: Code,
    difficulty: 'Advanced',
    estimatedTime: '15 minutes',
    steps: [
      'Use the Custom Structure input to paste your folder structure',
      'Ensure proper formatting with folders ending in /',
      'Add file extensions for better organization',
      'Test the structure generation',
      'Save as a custom template for future use',
    ],
  },
]

const HelpCenter: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'faq' | 'guides' | 'contact'>('faq')

  const categories = ['All', ...Array.from(new Set(faqData.map(item => item.category)))]

  const filteredFAQ = faqData.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <HelpCircle className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Help Center</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Find answers to common questions, learn how to use ProjectForge effectively, 
          and get support when you need it.
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative max-w-md mx-auto">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        <input
          type="text"
          placeholder="Search for help..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Tabs */}
      <div className="flex justify-center">
        <div className="flex bg-gray-100 rounded-lg p-1">
          {[
            { id: 'faq', label: 'FAQ', icon: HelpCircle },
            { id: 'guides', label: 'Guides', icon: Book },
            { id: 'contact', label: 'Contact', icon: MessageCircle },
          ].map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  'flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'faq' && (
          <motion.div
            key="faq"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Category Filter */}
            <div className="flex flex-wrap gap-2 justify-center">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={cn(
                    'px-3 py-1 rounded-full text-sm transition-colors',
                    selectedCategory === category
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  )}
                >
                  {category}
                </button>
              ))}
            </div>

            {/* FAQ Items */}
            <div className="space-y-4">
              {filteredFAQ.map(item => (
                <div key={item.id} className="bg-white rounded-lg border shadow-sm">
                  <button
                    onClick={() => toggleFAQ(item.id)}
                    className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                  >
                    <span className="font-medium text-gray-900">{item.question}</span>
                    {expandedFAQ === item.id ? (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                  
                  <AnimatePresence>
                    {expandedFAQ === item.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="overflow-hidden"
                      >
                        <div className="p-4 pt-0 border-t">
                          <p className="text-gray-600 mb-3">{item.answer}</p>
                          <div className="flex flex-wrap gap-2">
                            {item.tags.map(tag => (
                              <span
                                key={tag}
                                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>

            {filteredFAQ.length === 0 && (
              <div className="text-center py-8">
                <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600">Try adjusting your search or browse different categories</p>
              </div>
            )}
          </motion.div>
        )}

        {activeTab === 'guides' && (
          <motion.div
            key="guides"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
          >
            {guides.map(guide => {
              const Icon = guide.icon
              return (
                <div key={guide.id} className="bg-white rounded-lg border shadow-sm p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{guide.title}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span>{guide.difficulty}</span>
                        <span>•</span>
                        <span>{guide.estimatedTime}</span>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{guide.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    {guide.steps.slice(0, 3).map((step, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </span>
                        <span className="text-gray-600">{step}</span>
                      </div>
                    ))}
                    {guide.steps.length > 3 && (
                      <div className="text-sm text-gray-500 ml-7">
                        +{guide.steps.length - 3} more steps
                      </div>
                    )}
                  </div>
                  
                  <Button variant="outline" className="w-full">
                    View Full Guide
                  </Button>
                </div>
              )
            })}
          </motion.div>
        )}

        {activeTab === 'contact' && (
          <motion.div
            key="contact"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="max-w-2xl mx-auto"
          >
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Get in Touch</h2>
              <p className="text-gray-600 mb-6">
                Can't find what you're looking for? We're here to help!
              </p>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                    <h3 className="font-medium">Live Chat</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Get instant help from our support team
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    Start Chat
                  </Button>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <ExternalLink className="h-5 w-5 text-green-600" />
                    <h3 className="font-medium">GitHub Issues</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Report bugs or request features
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    Open Issue
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default HelpCenter
