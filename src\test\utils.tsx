import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ToastProvider } from '../components/ui/Toast'
import { useAppStore } from '../store/useAppStore'

// Mock the store for testing
export const createMockStore = (initialState: Partial<ReturnType<typeof useAppStore.getState>> = {}) => {
  const defaultState = {
    currentProject: null,
    isGenerating: false,
    mode: 'template' as const,
    projectHistory: [],
    favoriteTemplates: [],
    customTemplates: [],
    sidebarOpen: true,
    activeTab: 'templates',
    searchQuery: '',
    selectedCategory: 'All',
    settings: {
      theme: 'light' as const,
      autoSave: true,
      showFileExtensions: true,
      defaultProjectName: 'my-project',
      recentProjectsLimit: 10,
      enableAnalytics: true,
    },
    analytics: {
      projectsGenerated: 0,
      templatesUsed: {},
      featuresUsed: {},
    },
  }

  return {
    ...defaultState,
    ...initialState,
    // Mock functions
    setCurrentProject: vi.fn(),
    setIsGenerating: vi.fn(),
    setMode: vi.fn(),
    addToHistory: vi.fn(),
    removeFromHistory: vi.fn(),
    clearHistory: vi.fn(),
    addToFavorites: vi.fn(),
    removeFromFavorites: vi.fn(),
    addCustomTemplate: vi.fn(),
    removeCustomTemplate: vi.fn(),
    setSidebarOpen: vi.fn(),
    setActiveTab: vi.fn(),
    setSearchQuery: vi.fn(),
    setSelectedCategory: vi.fn(),
    updateSettings: vi.fn(),
    incrementProjectsGenerated: vi.fn(),
    incrementTemplateUsed: vi.fn(),
    incrementFeatureUsed: vi.fn(),
    reset: vi.fn(),
  }
}

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ToastProvider>
      {children}
    </ToastProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Test utilities
export const mockFileNode = (name: string, type: 'file' | 'directory' = 'file', children?: any[]) => ({
  name,
  type,
  children,
  content: type === 'file' ? `// ${name}\n// Generated content` : undefined,
})

export const mockProject = (overrides = {}) => ({
  name: 'test-project',
  frontend: 'React',
  backend: 'Node.js',
  database: 'MongoDB',
  structure: mockFileNode('test-project', 'directory', [
    mockFileNode('src', 'directory', [
      mockFileNode('App.tsx'),
      mockFileNode('index.tsx'),
    ]),
    mockFileNode('package.json'),
    mockFileNode('README.md'),
  ]),
  ...overrides,
})

export const mockTemplate = (overrides = {}) => ({
  id: 'test-template',
  name: 'Test Template',
  description: 'A test template for testing',
  category: 'Frontend',
  tags: ['React', 'TypeScript'],
  structure: mockFileNode('test-template', 'directory'),
  popularity: 85,
  lastUpdated: '2024-01-15',
  author: 'Test Author',
  downloads: 1000,
  ...overrides,
})

// Mock API responses
export const mockApiResponse = <T>(data: T, delay = 0): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message = 'API Error', delay = 0): Promise<never> => {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error(message)), delay)
  })
}

// User event helpers
export const createUserEvent = async () => {
  const { userEvent } = await import('@testing-library/user-event')
  return userEvent.setup()
}

// Wait for async operations
export const waitForAsync = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms))

// Mock intersection observer
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.IntersectionObserver = mockIntersectionObserver
  window.IntersectionObserverEntry = vi.fn()
}

// Mock resize observer
export const mockResizeObserver = () => {
  const mockResizeObserver = vi.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.ResizeObserver = mockResizeObserver
  window.ResizeObserverEntry = vi.fn()
}

// Test data generators
export const generateTestProjects = (count: number) => {
  return Array.from({ length: count }, (_, i) => mockProject({
    name: `test-project-${i + 1}`,
    id: `project-${i + 1}`,
  }))
}

export const generateTestTemplates = (count: number) => {
  return Array.from({ length: count }, (_, i) => mockTemplate({
    id: `template-${i + 1}`,
    name: `Test Template ${i + 1}`,
    downloads: Math.floor(Math.random() * 10000),
    popularity: Math.floor(Math.random() * 100),
  }))
}

// Custom matchers
expect.extend({
  toBeInTheDocument: (received) => {
    const pass = received && document.body.contains(received)
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass,
    }
  },
})

// Mock zustand store
export const mockZustandStore = <T>(initialState: T) => {
  let state = initialState
  
  return {
    getState: () => state,
    setState: (newState: Partial<T>) => {
      state = { ...state, ...newState }
    },
    subscribe: vi.fn(),
    destroy: vi.fn(),
  }
}
