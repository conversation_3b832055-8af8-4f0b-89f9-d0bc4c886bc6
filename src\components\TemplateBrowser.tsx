import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Search,
  Filter,
  Heart,
  Download,
  Star,
  Clock,
  User,
  Tag,
  Grid,
  List,
  SortAsc,
  SortDesc,
  BookOpen,
  Zap,
  Award,
  Target,
  Lightbulb,
  GraduationCap
} from 'lucide-react'
import { Button } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { projectTemplates, templateCategories, popularTags, difficultyLevels, beginnerTemplates } from '../data/templates'
import { ProjectTemplate } from '../store/useAppStore'
import { cn } from '../lib/utils'

type ViewMode = 'grid' | 'list'
type SortBy = 'popularity' | 'name' | 'downloads' | 'updated'
type SortOrder = 'asc' | 'desc'

const TemplateBrowser: React.FC = () => {
  const {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    favoriteTemplates,
    addToFavorites,
    removeFromFavorites,
    setCurrentProject,
    incrementTemplateUsed,
    incrementFeatureUsed,
  } = useAppStore()

  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('popularity')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('All Levels')
  const [showBeginnerSection, setShowBeginnerSection] = useState(true)

  // Filter and sort templates
  const filteredTemplates = useMemo(() => {
    let filtered = projectTemplates

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by category
    if (selectedCategory && selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory)
    }

    // Filter by tags
    if (selectedTags.length > 0) {
      filtered = filtered.filter(template =>
        selectedTags.every(tag => template.tags.includes(tag))
      )
    }

    // Filter by favorites
    if (showFavoritesOnly) {
      filtered = filtered.filter(template => favoriteTemplates.includes(template.id))
    }

    // Filter by difficulty
    if (selectedDifficulty && selectedDifficulty !== 'All Levels') {
      filtered = filtered.filter(template => template.difficulty === selectedDifficulty)
    }

    // Sort templates
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'popularity':
          comparison = a.popularity - b.popularity
          break
        case 'downloads':
          comparison = a.downloads - b.downloads
          break
        case 'updated':
          comparison = new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime()
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filtered
  }, [searchQuery, selectedCategory, selectedTags, showFavoritesOnly, sortBy, sortOrder, favoriteTemplates])

  const handleTemplateSelect = (template: ProjectTemplate) => {
    setCurrentProject({
      name: template.name.toLowerCase().replace(/[^a-z0-9]/g, '-'),
      frontend: 'template',
      backend: null,
      database: null,
      structure: template.structure,
    })
    
    incrementTemplateUsed(template.id)
    incrementFeatureUsed('template-selection')
  }

  const toggleFavorite = (templateId: string) => {
    if (favoriteTemplates.includes(templateId)) {
      removeFromFavorites(templateId)
    } else {
      addToFavorites(templateId)
    }
    incrementFeatureUsed('favorites')
  }

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap items-center gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              {templateCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            {/* Difficulty Filter */}
            <select
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              {difficultyLevels.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-l-md',
                  viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                )}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-r-md border-l',
                  viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                )}
              >
                <List className="h-4 w-4" />
              </button>
            </div>

            {/* Sort Controls */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortBy)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="popularity">Popularity</option>
              <option value="name">Name</option>
              <option value="downloads">Downloads</option>
              <option value="updated">Last Updated</option>
            </select>

            <button
              onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </button>

            {/* Favorites Toggle */}
            <button
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              className={cn(
                'flex items-center px-3 py-2 rounded-md border',
                showFavoritesOnly
                  ? 'bg-red-50 border-red-200 text-red-700'
                  : 'border-gray-300 text-gray-600 hover:bg-gray-50'
              )}
            >
              <Heart className={cn('h-4 w-4 mr-2', showFavoritesOnly && 'fill-current')} />
              Favorites Only
            </button>
          </div>

          {/* Popular Tags */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Popular Tags:</label>
            <div className="flex flex-wrap gap-2">
              {popularTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => toggleTag(tag)}
                  className={cn(
                    'px-3 py-1 rounded-full text-sm border transition-colors',
                    selectedTags.includes(tag)
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-600 border-gray-300 hover:border-blue-300'
                  )}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Beginner Templates Section */}
      {showBeginnerSection && selectedCategory === 'All' && !searchQuery && selectedDifficulty === 'All Levels' && (
        <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <GraduationCap className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">🚀 Perfect for Beginners</h3>
                <p className="text-gray-600">Start your coding journey with these beginner-friendly templates</p>
              </div>
            </div>
            <button
              onClick={() => setShowBeginnerSection(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {beginnerTemplates.slice(0, 3).map(template => (
              <div key={template.id} className="bg-white rounded-lg p-4 border border-gray-200 hover:border-green-300 transition-colors">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{template.name}</h4>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    {template.difficulty}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{template.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    {template.estimatedTime}
                  </div>
                  <Button
                    size="sm"
                    onClick={() => handleTemplateSelect(template)}
                    className="text-xs"
                  >
                    Use Template
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 text-center">
            <Button
              variant="outline"
              onClick={() => {
                setSelectedDifficulty('Beginner')
                setShowBeginnerSection(false)
              }}
            >
              View All Beginner Templates
            </Button>
          </div>
        </div>
      )}

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
        </p>
      </div>

      {/* Templates Grid/List */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className={cn(
            'grid gap-6',
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          )}
        >
          {filteredTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              viewMode={viewMode}
              isFavorite={favoriteTemplates.includes(template.id)}
              onSelect={() => handleTemplateSelect(template)}
              onToggleFavorite={() => toggleFavorite(template.id)}
            />
          ))}
        </motion.div>
      </AnimatePresence>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600">Try adjusting your search criteria or filters</p>
        </div>
      )}
    </div>
  )
}

interface TemplateCardProps {
  template: ProjectTemplate
  viewMode: ViewMode
  isFavorite: boolean
  onSelect: () => void
  onToggleFavorite: () => void
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  viewMode,
  isFavorite,
  onSelect,
  onToggleFavorite,
}) => {
  if (viewMode === 'list') {
    return (
      <motion.div
        layout
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-lg border shadow-sm p-6 hover:shadow-md transition-shadow"
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {template.category}
              </span>
              <span className={cn(
                'px-2 py-1 text-xs rounded-full',
                template.difficulty === 'Beginner' && 'bg-green-100 text-green-800',
                template.difficulty === 'Intermediate' && 'bg-yellow-100 text-yellow-800',
                template.difficulty === 'Advanced' && 'bg-red-100 text-red-800'
              )}>
                {template.difficulty}
              </span>
            </div>
            <p className="text-gray-600 mb-3">{template.description}</p>

            {/* Features */}
            <div className="flex flex-wrap gap-1 mb-3">
              {template.features.slice(0, 4).map(feature => (
                <span
                  key={feature}
                  className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
              {template.features.length > 4 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  +{template.features.length - 4} more
                </span>
              )}
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {template.estimatedTime}
              </span>
              <span className="flex items-center gap-1">
                <Star className="h-4 w-4" />
                {template.popularity}%
              </span>
              <span className="flex items-center gap-1">
                <Download className="h-4 w-4" />
                {template.downloads.toLocaleString()}
              </span>
              <span className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {template.author}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onToggleFavorite}
              className={cn(
                'p-2 rounded-md transition-colors',
                isFavorite
                  ? 'text-red-500 hover:bg-red-50'
                  : 'text-gray-400 hover:text-red-500 hover:bg-gray-50'
              )}
            >
              <Heart className={cn('h-5 w-5', isFavorite && 'fill-current')} />
            </button>
            <Button onClick={onSelect} variant="gradient">
              Use Template
            </Button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ y: -4 }}
      className="bg-white rounded-lg border shadow-sm overflow-hidden hover:shadow-lg transition-all duration-200"
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
            <div className="flex items-center gap-2 mb-1">
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {template.category}
              </span>
              <span className={cn(
                'px-2 py-1 text-xs rounded-full',
                template.difficulty === 'Beginner' && 'bg-green-100 text-green-800',
                template.difficulty === 'Intermediate' && 'bg-yellow-100 text-yellow-800',
                template.difficulty === 'Advanced' && 'bg-red-100 text-red-800'
              )}>
                {template.difficulty}
              </span>
            </div>
          </div>
          <button
            onClick={onToggleFavorite}
            className={cn(
              'p-1 rounded-md transition-colors',
              isFavorite
                ? 'text-red-500 hover:bg-red-50'
                : 'text-gray-400 hover:text-red-500 hover:bg-gray-50'
            )}
          >
            <Heart className={cn('h-4 w-4', isFavorite && 'fill-current')} />
          </button>
        </div>

        <p className="text-gray-600 text-sm mb-4">{template.description}</p>

        <div className="flex flex-wrap gap-1 mb-4">
          {template.tags.slice(0, 3).map(tag => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
            >
              {tag}
            </span>
          ))}
          {template.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
              +{template.tags.length - 3}
            </span>
          )}
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-4">
          <span className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {template.estimatedTime}
          </span>
          <span className="flex items-center gap-1">
            <Download className="h-3 w-3" />
            {template.downloads.toLocaleString()}
          </span>
          <span className="flex items-center gap-1">
            <Star className="h-3 w-3" />
            {template.popularity}%
          </span>
          <span className="flex items-center gap-1">
            <Target className="h-3 w-3" />
            {template.features.length} features
          </span>
        </div>

        {/* Key Features */}
        {template.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {template.features.slice(0, 2).map(feature => (
                <span
                  key={feature}
                  className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
              {template.features.length > 2 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  +{template.features.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        <Button onClick={onSelect} variant="gradient" className="w-full">
          Use Template
        </Button>
      </div>
    </motion.div>
  )
}

export default TemplateBrowser
