import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  ArrowLeft, 
  Sparkles, 
  LayoutTemplate, 
  BarChart3, 
  Download,
  CheckCircle
} from 'lucide-react'
import { Button } from './ui/Button'
import { useAppStore } from '../store/useAppStore'

interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  target?: string
  action?: () => void
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to ProjectForge!',
    description: 'Your AI-powered project structure generator. Let\'s take a quick tour to get you started.',
    icon: Sparkles,
  },
  {
    id: 'templates',
    title: 'Browse Templates',
    description: 'Explore our curated collection of modern project templates. Filter by technology, category, or search for specific frameworks.',
    icon: LayoutTemplate,
    target: 'templates-tab',
    action: () => useAppStore.getState().setActiveTab('templates'),
  },
  {
    id: 'ai-generator',
    title: 'AI Project Generator',
    description: 'Describe your project idea and let our AI create a custom structure tailored to your needs.',
    icon: <PERSON>rk<PERSON>,
    target: 'ai-tab',
    action: () => useAppStore.getState().setActiveTab('ai'),
  },
  {
    id: 'analytics',
    title: 'Track Your Progress',
    description: 'View analytics about your project generation patterns and discover insights about your development workflow.',
    icon: BarChart3,
    target: 'analytics-tab',
    action: () => useAppStore.getState().setActiveTab('analytics'),
  },
  {
    id: 'download',
    title: 'Download & Deploy',
    description: 'Once you\'ve generated a project, download it as a ZIP file or deploy directly to popular platforms.',
    icon: Download,
  },
]

const OnboardingTour: React.FC = () => {
  const { settings, updateSettings } = useAppStore()
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Show onboarding for new users
    const hasSeenOnboarding = localStorage.getItem('projectforge-onboarding-completed')
    if (!hasSeenOnboarding) {
      setIsVisible(true)
    }
  }, [])

  const handleNext = () => {
    const step = onboardingSteps[currentStep]
    if (step.action) {
      step.action()
    }
    
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    localStorage.setItem('projectforge-onboarding-completed', 'true')
    setIsVisible(false)
  }

  const handleSkip = () => {
    localStorage.setItem('projectforge-onboarding-completed', 'true')
    setIsVisible(false)
  }

  if (!isVisible) return null

  const step = onboardingSteps[currentStep]
  const Icon = step.icon
  const isLastStep = currentStep === onboardingSteps.length - 1

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <Icon className="h-6 w-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">{step.title}</h2>
                  <p className="text-blue-100 text-sm">
                    Step {currentStep + 1} of {onboardingSteps.length}
                  </p>
                </div>
              </div>
              <button
                onClick={handleSkip}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <p className="text-gray-600 mb-6 leading-relaxed">
              {step.description}
            </p>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-500 mb-2">
                <span>Progress</span>
                <span>{Math.round(((currentStep + 1) / onboardingSteps.length) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ 
                    width: `${((currentStep + 1) / onboardingSteps.length) * 100}%` 
                  }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Button
                onClick={handlePrevious}
                variant="outline"
                disabled={currentStep === 0}
                leftIcon={<ArrowLeft className="h-4 w-4" />}
              >
                Previous
              </Button>

              <div className="flex gap-2">
                {onboardingSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentStep
                        ? 'bg-blue-600'
                        : index < currentStep
                        ? 'bg-green-500'
                        : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <Button
                onClick={isLastStep ? handleComplete : handleNext}
                variant="gradient"
                rightIcon={
                  isLastStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <ArrowRight className="h-4 w-4" />
                  )
                }
              >
                {isLastStep ? 'Get Started' : 'Next'}
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

// Hook to manually trigger onboarding
export const useOnboarding = () => {
  const [isVisible, setIsVisible] = useState(false)

  const startOnboarding = () => {
    setIsVisible(true)
  }

  const resetOnboarding = () => {
    localStorage.removeItem('projectforge-onboarding-completed')
    setIsVisible(true)
  }

  return {
    isVisible,
    startOnboarding,
    resetOnboarding,
    OnboardingComponent: () => isVisible ? <OnboardingTour /> : null,
  }
}

export default OnboardingTour
