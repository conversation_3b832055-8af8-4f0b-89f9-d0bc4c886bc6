import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  X, 
  SlidersHorizontal,
  Star,
  Clock,
  Download,
  TrendingUp
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { templateCategories, popularTags } from '../data/templates'
import { cn, debounce } from '../lib/utils'

interface SearchAndFiltersProps {
  onFiltersChange?: (filters: any) => void
}

const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({ onFiltersChange }) => {
  const {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
  } = useAppStore()

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'popularity' | 'name' | 'downloads' | 'updated'>('popularity')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [minDownloads, setMinDownloads] = useState<number>(0)
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)

  // Debounced search to improve performance
  const debouncedSearch = debounce((query: string) => {
    setSearchQuery(query)
  }, 300)

  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange({
        searchQuery,
        selectedCategory,
        selectedTags,
        sortBy,
        sortOrder,
        minDownloads,
        showFavoritesOnly,
      })
    }
  }, [searchQuery, selectedCategory, selectedTags, sortBy, sortOrder, minDownloads, showFavoritesOnly])

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const clearAllFilters = () => {
    setSearchQuery('')
    setSelectedCategory('All')
    setSelectedTags([])
    setSortBy('popularity')
    setSortOrder('desc')
    setMinDownloads(0)
    setShowFavoritesOnly(false)
  }

  const hasActiveFilters = 
    searchQuery || 
    selectedCategory !== 'All' || 
    selectedTags.length > 0 || 
    minDownloads > 0 || 
    showFavoritesOnly

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        <input
          type="text"
          placeholder="Search templates, technologies, or descriptions..."
          defaultValue={searchQuery}
          onChange={(e) => debouncedSearch(e.target.value)}
          className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Quick Filters Row */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Category Filter */}
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm"
        >
          {templateCategories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>

        {/* Sort Options */}
        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [newSortBy, newSortOrder] = e.target.value.split('-')
            setSortBy(newSortBy as any)
            setSortOrder(newSortOrder as any)
          }}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm"
        >
          <option value="popularity-desc">Most Popular</option>
          <option value="downloads-desc">Most Downloaded</option>
          <option value="updated-desc">Recently Updated</option>
          <option value="name-asc">Name A-Z</option>
          <option value="name-desc">Name Z-A</option>
        </select>

        {/* Favorites Toggle */}
        <Button
          onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
          variant={showFavoritesOnly ? "default" : "outline"}
          size="sm"
          leftIcon={<Star className={cn("h-4 w-4", showFavoritesOnly && "fill-current")} />}
        >
          Favorites
        </Button>

        {/* Advanced Filters Toggle */}
        <Button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          variant="outline"
          size="sm"
          leftIcon={<SlidersHorizontal className="h-4 w-4" />}
        >
          Filters
          {hasActiveFilters && (
            <span className="ml-1 px-1.5 py-0.5 bg-blue-500 text-white text-xs rounded-full">
              {[
                searchQuery && 1,
                selectedCategory !== 'All' && 1,
                selectedTags.length,
                minDownloads > 0 && 1,
                showFavoritesOnly && 1,
              ].filter(Boolean).reduce((a, b) => (a as number) + (b as number), 0)}
            </span>
          )}
        </Button>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button
            onClick={clearAllFilters}
            variant="ghost"
            size="sm"
            leftIcon={<X className="h-4 w-4" />}
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Advanced Filters Panel */}
      <AnimatePresence>
        {showAdvancedFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              {/* Technology Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Technologies
                </label>
                <div className="flex flex-wrap gap-2">
                  {popularTags.map(tag => (
                    <button
                      key={tag}
                      onClick={() => toggleTag(tag)}
                      className={cn(
                        'px-3 py-1 rounded-full text-sm border transition-colors',
                        selectedTags.includes(tag)
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-blue-300'
                      )}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>

              {/* Minimum Downloads */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Downloads: {minDownloads.toLocaleString()}
                </label>
                <input
                  type="range"
                  min="0"
                  max="50000"
                  step="1000"
                  value={minDownloads}
                  onChange={(e) => setMinDownloads(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0</span>
                  <span>50K+</span>
                </div>
              </div>

              {/* Quick Filter Presets */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quick Presets
                </label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={() => {
                      setSelectedTags(['React', 'TypeScript'])
                      setSortBy('popularity')
                      setSortOrder('desc')
                    }}
                    variant="outline"
                    size="sm"
                  >
                    React + TypeScript
                  </Button>
                  <Button
                    onClick={() => {
                      setSelectedTags(['Vue.js', 'TypeScript'])
                      setSortBy('popularity')
                      setSortOrder('desc')
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Vue + TypeScript
                  </Button>
                  <Button
                    onClick={() => {
                      setSelectedTags(['Node.js', 'Express.js'])
                      setSortBy('popularity')
                      setSortOrder('desc')
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Node.js Backend
                  </Button>
                  <Button
                    onClick={() => {
                      setSelectedTags(['Python', 'FastAPI'])
                      setSortBy('popularity')
                      setSortOrder('desc')
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Python API
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchQuery && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              Search: "{searchQuery}"
              <button onClick={() => setSearchQuery('')}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          
          {selectedCategory !== 'All' && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
              Category: {selectedCategory}
              <button onClick={() => setSelectedCategory('All')}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          
          {selectedTags.map(tag => (
            <span
              key={tag}
              className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
            >
              {tag}
              <button onClick={() => toggleTag(tag)}>
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
          
          {minDownloads > 0 && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
              Min Downloads: {minDownloads.toLocaleString()}
              <button onClick={() => setMinDownloads(0)}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          
          {showFavoritesOnly && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
              Favorites Only
              <button onClick={() => setShowFavoritesOnly(false)}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default SearchAndFilters
