import { GoogleGenerativeAI } from '@google/generative-ai'
import { FileNode } from '../types'
import { logger } from '../utils/logger'

export interface AIProjectRequest {
  description: string
  projectType: 'web' | 'mobile' | 'desktop' | 'api' | 'library' | 'cli'
  technologies: string[]
  features: string[]
  complexity: 'simple' | 'medium' | 'complex'
  includeTests: boolean
  includeDocumentation: boolean
}

export interface AIProjectResponse {
  projectName: string
  description: string
  structure: FileNode
  recommendations: string[]
  estimatedTime: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  aiProvider: string
}

export interface AIServiceConfig {
  apiKey: string
  model: string
  maxRetries: number
  retryDelay: number
}

export class AIProjectService {
  private static genAI: GoogleGenerativeAI | null = null
  private static config: AIServiceConfig = (() => {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY || ''
    console.log('🔑 AI Service Config Debug:', {
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey.length,
      apiKeyStart: apiKey.substring(0, 10),
      allEnvVars: Object.keys(import.meta.env).filter(k => k.startsWith('VITE_')),
      enableAI: import.meta.env.VITE_ENABLE_AI_GENERATION
    })

    return {
      apiKey,
      model: 'gemini-1.5-flash',
      maxRetries: 3,
      retryDelay: 1000,
    }
  })()

  private static initializeAI(): void {
    console.log('🔧 Initializing AI with config:', {
      hasApiKey: !!this.config.apiKey,
      apiKeyLength: this.config.apiKey?.length || 0,
      model: this.config.model
    })

    if (!this.config.apiKey) {
      throw new Error('Gemini API key not configured. Please set VITE_GEMINI_API_KEY environment variable.')
    }

    if (!this.genAI) {
      this.genAI = new GoogleGenerativeAI(this.config.apiKey)
      console.log('✅ Google Gemini AI initialized successfully')
      logger.info('Google Gemini AI initialized', { model: this.config.model }, 'ai-service')
    }
  }

  static async generateProject(request: AIProjectRequest): Promise<AIProjectResponse> {
    try {
      this.initializeAI()

      logger.info('Starting AI project generation', {
        projectType: request.projectType,
        technologies: request.technologies,
        complexity: request.complexity
      }, 'ai-service')

      const prompt = this.buildPrompt(request)
      const response = await this.callGeminiWithRetry(prompt)

      const parsedResponse = this.parseAIResponse(response, request)

      logger.info('AI project generation completed', {
        projectName: parsedResponse.projectName,
        difficulty: parsedResponse.difficulty
      }, 'ai-service')

      return parsedResponse
    } catch (error) {
      logger.error('AI project generation failed', error, 'ai-service')

      // Fallback to basic generation if AI fails
      logger.info('Falling back to basic project generation', undefined, 'ai-service')
      return this.generateFallbackProject(request)
    }
  }

  private static buildPrompt(request: AIProjectRequest): string {
    return `You are an expert software architect. Generate a detailed project structure based on the following requirements:

Project Description: ${request.description}
Project Type: ${request.projectType}
Technologies: ${request.technologies.join(', ')}
Features: ${request.features.join(', ')}
Complexity: ${request.complexity}
Include Tests: ${request.includeTests}
Include Documentation: ${request.includeDocumentation}

Please respond with a JSON object containing:
1. projectName: A kebab-case project name based on the description
2. structure: A nested file/folder structure with realistic file names and basic content
3. recommendations: Array of 3-5 specific recommendations for this project
4. estimatedTime: Development time estimate (e.g., "2-3 weeks")
5. difficulty: "Beginner", "Intermediate", or "Advanced"

The structure should be a nested object where:
- Each folder has: { name: "folder-name", type: "directory", children: [...] }
- Each file has: { name: "file-name.ext", type: "file", content: "basic file content" }

Make the structure realistic and follow modern best practices for the chosen technologies.

Example structure format:
{
  "projectName": "my-awesome-app",
  "structure": {
    "name": "my-awesome-app",
    "type": "directory",
    "children": [
      {
        "name": "src",
        "type": "directory",
        "children": [
          {
            "name": "App.tsx",
            "type": "file",
            "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
          }
        ]
      },
      {
        "name": "package.json",
        "type": "file",
        "content": "{\\n  \\"name\\": \\"my-awesome-app\\",\\n  \\"version\\": \\"1.0.0\\"\\n}"
      }
    ]
  },
  "recommendations": ["Use TypeScript for better type safety", "Add ESLint for code quality"],
  "estimatedTime": "2-3 weeks",
  "difficulty": "Intermediate"
}`
  }

  private static async callGeminiWithRetry(prompt: string): Promise<string> {
    if (!this.genAI) {
      throw new Error('Gemini AI not initialized')
    }

    const model = this.genAI.getGenerativeModel({ model: this.config.model })

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        logger.debug(`Gemini API call attempt ${attempt}`, { model: this.config.model }, 'ai-service')

        const result = await model.generateContent(prompt)
        const response = await result.response
        const text = response.text()

        if (!text) {
          throw new Error('Empty response from Gemini API')
        }

        logger.debug('Gemini API call successful', {
          responseLength: text.length,
          attempt
        }, 'ai-service')

        return text
      } catch (error: any) {
        logger.warn(`Gemini API call failed (attempt ${attempt})`, {
          error: error.message,
          attempt,
          maxRetries: this.config.maxRetries
        }, 'ai-service')

        if (attempt === this.config.maxRetries) {
          throw new Error(`Gemini API failed after ${this.config.maxRetries} attempts: ${error.message}`)
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt))
      }
    }

    throw new Error('Unexpected error in Gemini API retry logic')
  }

  private static parseAIResponse(aiResponse: string, request: AIProjectRequest): AIProjectResponse {
    try {
      // Extract JSON from the response (handle markdown code blocks)
      const jsonMatch = aiResponse.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                       aiResponse.match(/(\{[\s\S]*\})/)

      if (!jsonMatch) {
        throw new Error('No JSON found in AI response')
      }

      const parsed = JSON.parse(jsonMatch[1])

      // Validate required fields
      if (!parsed.projectName || !parsed.structure || !parsed.recommendations) {
        throw new Error('Invalid AI response structure')
      }

      return {
        projectName: this.sanitizeProjectName(parsed.projectName),
        description: request.description,
        structure: this.validateAndCleanStructure(parsed.structure),
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations.slice(0, 5) : [],
        estimatedTime: parsed.estimatedTime || this.estimateTime(request.complexity),
        difficulty: this.validateDifficulty(parsed.difficulty) || this.determineDifficulty(request),
        aiProvider: 'Google Gemini'
      }
    } catch (error) {
      logger.error('Failed to parse AI response', { error, response: aiResponse.substring(0, 500) }, 'ai-service')
      throw new Error('Failed to parse AI response')
    }
  }

  private static sanitizeProjectName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9-_\s]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50) || 'ai-generated-project'
  }

  private static validateDifficulty(difficulty: string): 'Beginner' | 'Intermediate' | 'Advanced' | null {
    const validDifficulties = ['Beginner', 'Intermediate', 'Advanced']
    return validDifficulties.includes(difficulty) ? difficulty as any : null
  }

  private static validateAndCleanStructure(structure: any): FileNode {
    if (!structure || typeof structure !== 'object') {
      throw new Error('Invalid structure format')
    }

    const cleanNode = (node: any): FileNode => {
      if (!node.name || !node.type) {
        throw new Error('Node missing required fields')
      }

      const cleanedNode: FileNode = {
        name: String(node.name).substring(0, 100), // Limit name length
        type: node.type === 'directory' ? 'directory' : 'file'
      }

      if (node.type === 'directory' && Array.isArray(node.children)) {
        cleanedNode.children = node.children.map(cleanNode).slice(0, 50) // Limit children
      } else if (node.type === 'file' && node.content) {
        cleanedNode.content = String(node.content).substring(0, 5000) // Limit content length
      }

      return cleanedNode
    }

    return cleanNode(structure)
  }

  private static generateFallbackProject(request: AIProjectRequest): AIProjectResponse {
    logger.info('Generating fallback project structure', { projectType: request.projectType }, 'ai-service')

    const projectName = this.generateProjectNameFromDescription(request.description)
    const structure = this.generateBasicProjectStructure(request)

    return {
      projectName,
      description: request.description,
      structure,
      recommendations: this.generateBasicRecommendations(request),
      estimatedTime: this.estimateTime(request.complexity),
      difficulty: this.determineDifficulty(request),
      aiProvider: 'Fallback Generator'
    }
  }

  private static generateProjectNameFromDescription(description: string): string {
    const words = description.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .slice(0, 3)

    return words.join('-') || 'generated-project'
  }

  private static generateBasicProjectStructure(request: AIProjectRequest): FileNode {
    const { projectType, technologies, features, includeTests, includeDocumentation } = request
    const projectName = this.generateProjectNameFromDescription(request.description)

    const createFile = (name: string, content?: string): FileNode => ({
      name,
      type: 'file',
      content: content || `// ${name}\n// Generated by ProjectForge\n`,
    })

    const createFolder = (name: string, children: FileNode[] = []): FileNode => ({
      name,
      type: 'directory',
      children,
    })

    // Base structure
    const baseFiles = [
      createFile('.gitignore', 'node_modules/\ndist/\n.env.local\n'),
      createFile('README.md', `# ${projectName}\n\nGenerated by ProjectForge\n\n## Getting Started\n\n\`\`\`bash\nnpm install\nnpm run dev\n\`\`\``),
      createFile('package.json', `{\n  "name": "${projectName}",\n  "version": "1.0.0",\n  "type": "module"\n}`),
    ]

    if (includeDocumentation) {
      baseFiles.push(
        createFolder('docs', [
          createFile('CONTRIBUTING.md', '# Contributing Guide\n\nThank you for contributing!'),
          createFile('DEPLOYMENT.md', '# Deployment Guide\n\nDeployment instructions here.'),
        ])
      )
    }

    // Technology-specific structure
    let srcStructure: FileNode[] = []

    if (technologies.includes('React') || technologies.includes('Vue') || technologies.includes('Angular')) {
      srcStructure = [
        createFile('main.tsx', 'import React from "react"\nimport ReactDOM from "react-dom/client"\nimport App from "./App"\n\nReactDOM.createRoot(document.getElementById("root")!).render(<App />)'),
        createFile('App.tsx', 'import React from "react"\n\nfunction App() {\n  return (\n    <div className="App">\n      <h1>Welcome to ' + projectName + '</h1>\n    </div>\n  )\n}\n\nexport default App'),
        createFile('index.css', 'body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";\n}'),
        createFolder('components', [
          createFile('Header.tsx', 'import React from "react"\n\nexport const Header = () => {\n  return <header>Header Component</header>\n}'),
          createFile('Button.tsx', 'import React from "react"\n\ninterface ButtonProps {\n  children: React.ReactNode\n  onClick?: () => void\n}\n\nexport const Button = ({ children, onClick }: ButtonProps) => {\n  return <button onClick={onClick}>{children}</button>\n}'),
        ]),
        createFolder('hooks', [
          createFile('useApi.ts', 'import { useState, useEffect } from "react"\n\nexport const useApi = (url: string) => {\n  const [data, setData] = useState(null)\n  const [loading, setLoading] = useState(true)\n  \n  useEffect(() => {\n    // API logic here\n    setLoading(false)\n  }, [url])\n  \n  return { data, loading }\n}'),
        ]),
        createFolder('utils', [
          createFile('helpers.ts', 'export const formatDate = (date: Date) => {\n  return date.toLocaleDateString()\n}\n\nexport const capitalize = (str: string) => {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}'),
        ]),
        createFolder('types', [
          createFile('index.ts', 'export interface User {\n  id: string\n  name: string\n  email: string\n}\n\nexport interface ApiResponse<T> {\n  data: T\n  success: boolean\n}'),
        ]),
      ]

      if (features.includes('routing')) {
        srcStructure.push(
          createFolder('pages', [
            createFile('Home.tsx', 'import React from "react"\n\nexport const Home = () => {\n  return <div>Home Page</div>\n}'),
            createFile('About.tsx', 'import React from "react"\n\nexport const About = () => {\n  return <div>About Page</div>\n}'),
          ])
        )
      }

      if (features.includes('state-management')) {
        srcStructure.push(
          createFolder('store', [
            createFile('index.ts', 'import { create } from "zustand"\n\ninterface AppState {\n  count: number\n  increment: () => void\n}\n\nexport const useStore = create<AppState>((set) => ({\n  count: 0,\n  increment: () => set((state) => ({ count: state.count + 1 })),\n}))'),
          ])
        )
      }
    }

    // Add testing structure if requested
    if (includeTests) {
      baseFiles.push(
        createFolder('tests', [
          createFile('setup.ts', 'import "@testing-library/jest-dom"'),
          createFolder('components', [
            createFile('App.test.tsx', 'import { render, screen } from "@testing-library/react"\nimport App from "../src/App"\n\ntest("renders app", () => {\n  render(<App />)\n  expect(screen.getByText(/welcome/i)).toBeInTheDocument()\n})'),
          ]),
        ])
      )
    }

    // Add configuration files based on technologies
    if (technologies.includes('TypeScript')) {
      baseFiles.push(createFile('tsconfig.json', '{\n  "compilerOptions": {\n    "target": "ES2020",\n    "lib": ["ES2020", "DOM", "DOM.Iterable"],\n    "module": "ESNext",\n    "skipLibCheck": true,\n    "moduleResolution": "bundler",\n    "allowImportingTsExtensions": true,\n    "resolveJsonModule": true,\n    "isolatedModules": true,\n    "noEmit": true,\n    "jsx": "react-jsx",\n    "strict": true\n  },\n  "include": ["src"]\n}'))
    }

    if (technologies.includes('Tailwind')) {
      baseFiles.push(
        createFile('tailwind.config.js', 'export default {\n  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n}'),
        createFile('postcss.config.js', 'export default {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}')
      )
    }

    if (technologies.includes('Vite')) {
      baseFiles.push(createFile('vite.config.ts', 'import { defineConfig } from "vite"\nimport react from "@vitejs/plugin-react"\n\nexport default defineConfig({\n  plugins: [react()],\n})'))
    }

    return createFolder(projectName, [
      ...baseFiles,
      createFolder('src', srcStructure),
      createFolder('public', [
        createFile('favicon.ico'),
        createFile('index.html', `<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8" />\n  <meta name="viewport" content="width=device-width, initial-scale=1.0" />\n  <title>${projectName}</title>\n</head>\n<body>\n  <div id="root"></div>\n  <script type="module" src="/src/main.tsx"></script>\n</body>\n</html>`),
      ]),
    ])
  }

  private static generateBasicRecommendations(request: AIProjectRequest): string[] {
    const recommendations: string[] = []

    if (!request.includeTests) {
      recommendations.push('Consider adding unit tests for better code quality and reliability')
    }

    if (!request.includeDocumentation) {
      recommendations.push('Add comprehensive documentation for better maintainability')
    }

    if (request.projectType === 'web' && !request.technologies.includes('TypeScript')) {
      recommendations.push('Consider using TypeScript for better type safety and developer experience')
    }

    if (request.features.includes('api-integration') && !request.features.includes('error-handling')) {
      recommendations.push('Implement proper error handling for API calls and user interactions')
    }

    if (request.complexity === 'complex' && !request.features.includes('state-management')) {
      recommendations.push('Consider using a state management solution like Zustand or Redux for complex applications')
    }

    recommendations.push('Set up CI/CD pipeline for automated testing and deployment')
    recommendations.push('Configure ESLint and Prettier for consistent code formatting')
    recommendations.push('Add environment variable configuration for different deployment environments')

    return recommendations.slice(0, 5)
  }

  private static estimateTime(complexity: 'simple' | 'medium' | 'complex'): string {
    switch (complexity) {
      case 'simple':
        return '1-2 weeks'
      case 'medium':
        return '3-6 weeks'
      case 'complex':
        return '2-4 months'
      default:
        return '2-4 weeks'
    }
  }

  private static determineDifficulty(request: AIProjectRequest): 'Beginner' | 'Intermediate' | 'Advanced' {
    const { complexity, technologies, features } = request

    if (complexity === 'simple' && technologies.length <= 2 && features.length <= 3) {
      return 'Beginner'
    }

    if (complexity === 'complex' || technologies.length > 4 || features.length > 6) {
      return 'Advanced'
    }

    return 'Intermediate'
  }

  // Public method to check if AI service is available
  static isAIServiceAvailable(): boolean {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY
    const enableAI = import.meta.env.VITE_ENABLE_AI_GENERATION

    // Debug logging
    console.log('🔍 AI Service Debug:', {
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      enableAI,
      envVars: Object.keys(import.meta.env).filter(key => key.startsWith('VITE_'))
    })

    return !!(apiKey && enableAI !== 'false')
  }

  // Public method to get AI service info
  static getAIServiceInfo(): { provider: string; model: string; available: boolean } {
    return {
      provider: 'Google Gemini',
      model: this.config.model,
      available: this.isAIServiceAvailable()
    }
  }

  // Public method to test AI connection
  static async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🧪 Testing AI connection...')

      if (!this.isAIServiceAvailable()) {
        const apiKey = import.meta.env.VITE_GEMINI_API_KEY
        const message = !apiKey
          ? 'VITE_GEMINI_API_KEY environment variable not set'
          : 'AI service disabled in configuration'
        return { success: false, message }
      }

      console.log('🔑 API key available, initializing AI...')
      this.initializeAI()

      console.log('📤 Sending test prompt...')
      const testPrompt = 'Respond with just the word "connected" if you can read this message.'
      const response = await this.callGeminiWithRetry(testPrompt)

      console.log('📥 Received response:', response)

      if (response.toLowerCase().includes('connected')) {
        return { success: true, message: 'AI service connected successfully' }
      } else {
        return { success: false, message: 'Unexpected response from AI service' }
      }
    } catch (error: any) {
      console.error('❌ AI connection test failed:', error)
      logger.error('AI connection test failed', error, 'ai-service')
      return { success: false, message: error.message || 'Connection test failed' }
    }
  }
}
