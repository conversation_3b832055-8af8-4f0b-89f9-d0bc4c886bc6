import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Bug, X, CheckCircle, AlertCircle, Info } from 'lucide-react'
import { AIProjectService } from '../services/aiService'

const DebugPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [connectionTest, setConnectionTest] = useState<any>(null)
  const [testing, setTesting] = useState(false)

  useEffect(() => {
    // Collect debug information
    const envVars = Object.keys(import.meta.env)
      .filter(key => key.startsWith('VITE_'))
      .reduce((acc, key) => {
        acc[key] = key.includes('API_KEY') 
          ? `${import.meta.env[key]?.substring(0, 10)}...${import.meta.env[key]?.slice(-4)}` 
          : import.meta.env[key]
        return acc
      }, {} as Record<string, any>)

    const aiServiceInfo = AIProjectService.getAIServiceInfo()
    const isAvailable = AIProjectService.isAIServiceAvailable()

    setDebugInfo({
      envVars,
      aiServiceInfo,
      isAvailable,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    })
  }, [])

  const testConnection = async () => {
    setTesting(true)
    try {
      const result = await AIProjectService.testConnection()
      setConnectionTest(result)
    } catch (error: any) {
      setConnectionTest({
        success: false,
        message: error.message || 'Test failed'
      })
    } finally {
      setTesting(false)
    }
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 p-3 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="Open Debug Panel"
      >
        <Bug className="h-5 w-5" />
      </button>
    )
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-4 bg-white rounded-lg shadow-2xl border z-50 overflow-hidden"
      >
        <div className="bg-gray-800 text-white p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Debug Panel</h2>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="p-1 hover:bg-gray-700 rounded"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(100vh-200px)]">
          {/* Environment Variables */}
          <section className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-500" />
              Environment Variables
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm overflow-x-auto">
                {JSON.stringify(debugInfo?.envVars || {}, null, 2)}
              </pre>
            </div>
          </section>

          {/* AI Service Info */}
          <section className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Info className="h-5 w-5 text-purple-500" />
              AI Service Information
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Provider:</strong> {debugInfo?.aiServiceInfo?.provider || 'Unknown'}
                </div>
                <div>
                  <strong>Model:</strong> {debugInfo?.aiServiceInfo?.model || 'Unknown'}
                </div>
                <div>
                  <strong>Available:</strong> 
                  <span className={`ml-2 ${debugInfo?.aiServiceInfo?.available ? 'text-green-600' : 'text-red-600'}`}>
                    {debugInfo?.aiServiceInfo?.available ? 'Yes' : 'No'}
                  </span>
                </div>
                <div>
                  <strong>Service Check:</strong>
                  <span className={`ml-2 ${debugInfo?.isAvailable ? 'text-green-600' : 'text-red-600'}`}>
                    {debugInfo?.isAvailable ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              </div>
            </div>
          </section>

          {/* Connection Test */}
          <section className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Connection Test
            </h3>
            <div className="space-y-3">
              <button
                onClick={testConnection}
                disabled={testing}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {testing ? 'Testing...' : 'Test AI Connection'}
              </button>

              {connectionTest && (
                <div className={`p-4 rounded-lg border ${
                  connectionTest.success 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {connectionTest.success ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <AlertCircle className="h-5 w-5" />
                    )}
                    <strong>
                      {connectionTest.success ? 'Success' : 'Failed'}
                    </strong>
                  </div>
                  <p className="text-sm">{connectionTest.message}</p>
                </div>
              )}
            </div>
          </section>

          {/* Browser Info */}
          <section className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Info className="h-5 w-5 text-gray-500" />
              Browser Information
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm space-y-2">
                <div><strong>User Agent:</strong> {debugInfo?.userAgent}</div>
                <div><strong>Timestamp:</strong> {debugInfo?.timestamp}</div>
                <div><strong>URL:</strong> {window.location.href}</div>
                <div><strong>Protocol:</strong> {window.location.protocol}</div>
              </div>
            </div>
          </section>

          {/* Console Logs */}
          <section>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Info className="h-5 w-5 text-orange-500" />
              Instructions
            </h3>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>To debug further:</strong>
              </p>
              <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                <li>• Open browser DevTools (F12)</li>
                <li>• Check the Console tab for error messages</li>
                <li>• Look for network errors in the Network tab</li>
                <li>• Verify environment variables are loaded correctly</li>
              </ul>
            </div>
          </section>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default DebugPanel
